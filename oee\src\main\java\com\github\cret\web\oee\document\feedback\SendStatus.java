package com.github.cret.web.oee.document.feedback;

/**
 * 发送状态枚举
 */
public enum SendStatus {

	/**
	 * 未发送
	 */
	UNSENT("UNSENT", "未发送"),

	/**
	 * 已发送
	 */
	SENT("SENT", "已发送"),

	/**
	 * 停止发送
	 */
	STOPPED("STOPPED", "停止发送");

	private final String code;

	private final String description;

	SendStatus(String code, String description) {
		this.code = code;
		this.description = description;
	}

	public String getCode() {
		return code;
	}

	public String getDescription() {
		return description;
	}

	/**
	 * 根据代码获取枚举值
	 * @param code 状态代码
	 * @return 对应的枚举值，如果不存在则返回null
	 */
	public static SendStatus fromCode(String code) {
		if (code == null) {
			return null;
		}
		for (SendStatus status : values()) {
			if (status.code.equals(code)) {
				return status;
			}
		}
		return null;
	}

	/**
	 * 根据Boolean值转换为对应的枚举值（用于向后兼容）
	 * @param booleanValue Boolean值：true-已发送，false-未发送，null-未发送
	 * @return 对应的枚举值
	 */
	public static SendStatus fromBoolean(Boolean booleanValue) {
		if (booleanValue == null || !booleanValue) {
			return UNSENT;
		}
		return SENT;
	}

	/**
	 * 转换为Boolean值（用于向后兼容）
	 * @return Boolean值：SENT-true，其他-false
	 */
	public Boolean toBoolean() {
		return this == SENT;
	}

	@Override
	public String toString() {
		return code;
	}

}
